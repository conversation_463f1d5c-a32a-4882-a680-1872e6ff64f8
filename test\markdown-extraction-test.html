<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown提取功能测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3 { color: #333; }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .code-block {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        blockquote {
            border-left: 4px solid #007acc;
            margin: 20px 0;
            padding: 10px 20px;
            background: #f9f9f9;
            font-style: italic;
        }
        .task-list {
            list-style: none;
            padding-left: 0;
        }
        .task-list li {
            margin: 5px 0;
        }
        .image-gallery {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        .image-item {
            flex: 1;
            min-width: 200px;
        }
        .image-item img {
            width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .highlight {
            background: yellow;
            padding: 2px 4px;
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🧪 Markdown提取功能测试页面</h1>
            <p class="test-info">
                <strong>测试说明：</strong>这个页面包含了各种Markdown元素，用于测试Chrome插件的增强Markdown提取功能。
                请使用插件的"提取Markdown"功能来测试以下元素的提取效果。
            </p>
        </header>

        <main>
            <section class="test-section">
                <h2>📝 标题层次测试</h2>
                <h3>这是三级标题</h3>
                <h4>这是四级标题</h4>
                <h5>这是五级标题</h5>
                <h6>这是六级标题</h6>
                <p>标题应该被正确转换为对应的Markdown语法（# ## ### 等）。</p>
            </section>

            <section class="test-section">
                <h2>🖼️ 图片提取测试</h2>
                <div class="image-gallery">
                    <div class="image-item">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iIzAwN2FjYyIvPgogIDx0ZXh0IHg9IjE1MCIgeT0iMTAwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTgiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+5rWL6K+V5Zu+54mHMTwvdGV4dD4KPC9zdmc+"
                             alt="测试图片1"
                             title="这是第一张测试图片">
                        <p>内联图片示例</p>
                    </div>
                    <div class="image-item">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iIzI4YTc0NSIvPgogIDx0ZXh0IHg9IjE1MCIgeT0iMTAwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTgiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+5rWL6K+V5Zu+54mHMjwvdGV4dD4KPC9zdmc+"
                             alt="测试图片2">
                        <p>带alt文本的图片</p>
                    </div>
                </div>
                <p>图片应该被转换为 <code>![alt text](image_url)</code> 格式。</p>
            </section>

            <section class="test-section">
                <h2>📊 表格提取测试</h2>
                <table>
                    <thead>
                        <tr>
                            <th>功能</th>
                            <th>状态</th>
                            <th>优先级</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>图片提取</td>
                            <td>✅ 已完成</td>
                            <td>高</td>
                            <td>支持内联和引用式图片</td>
                        </tr>
                        <tr>
                            <td>表格提取</td>
                            <td>✅ 已完成</td>
                            <td>高</td>
                            <td>保持表格结构和对齐</td>
                        </tr>
                        <tr>
                            <td>链接提取</td>
                            <td>✅ 已完成</td>
                            <td>中</td>
                            <td>转换为Markdown链接格式</td>
                        </tr>
                        <tr>
                            <td>代码块提取</td>
                            <td>✅ 已完成</td>
                            <td>中</td>
                            <td>保留语言标识</td>
                        </tr>
                    </tbody>
                </table>
                <p>表格应该被转换为标准的Markdown表格格式，包含表头分隔线。</p>
            </section>

            <section class="test-section">
                <h2>🔗 链接提取测试</h2>
                <p>这里有一些测试链接：</p>
                <ul>
                    <li><a href="https://www.example.com">外部链接示例</a></li>
                    <li><a href="/relative-link">相对链接示例</a></li>
                    <li><a href="mailto:<EMAIL>">邮件链接</a></li>
                    <li><a href="https://github.com/markdown" title="GitHub Markdown">带标题的链接</a></li>
                </ul>
                <p>链接应该被转换为 <code>[链接文本](URL)</code> 格式，相对链接应该转换为绝对链接。</p>
            </section>

            <section class="test-section">
                <h2>💻 代码提取测试</h2>
                <p>行内代码示例：<code>console.log('Hello World')</code></p>
                
                <h3>代码块示例：</h3>
                <div class="code-block">
function extractMarkdown(element) {
    const converter = new HTMLToMarkdownConverter();
    return converter.convert(element);
}

// 这是一个JavaScript代码块
const result = extractMarkdown(document.body);
console.log(result);
                </div>
                
                <p>代码应该被转换为行内代码（`code`）或代码块（```）格式。</p>
            </section>

            <section class="test-section">
                <h2>📋 列表提取测试</h2>
                
                <h3>无序列表：</h3>
                <ul>
                    <li>第一个列表项</li>
                    <li>第二个列表项
                        <ul>
                            <li>嵌套列表项1</li>
                            <li>嵌套列表项2</li>
                        </ul>
                    </li>
                    <li>第三个列表项</li>
                </ul>

                <h3>有序列表：</h3>
                <ol>
                    <li>步骤一：分析页面结构</li>
                    <li>步骤二：提取HTML元素</li>
                    <li>步骤三：转换为Markdown格式</li>
                    <li>步骤四：优化和清理</li>
                </ol>

                <h3>任务列表：</h3>
                <ul class="task-list">
                    <li>☑️ 已完成的任务</li>
                    <li>☐ 待完成的任务</li>
                    <li>☑️ 另一个已完成的任务</li>
                </ul>
                
                <p>列表应该被转换为对应的Markdown列表格式（- 或 1. ），包括嵌套结构。</p>
            </section>

            <section class="test-section">
                <h2>💬 引用块测试</h2>
                <blockquote>
                    <p>这是一个引用块的示例。引用块通常用于引用其他来源的内容或者突出显示重要信息。</p>
                    <p>引用块可以包含多个段落，每个段落都应该以 > 符号开头。</p>
                </blockquote>
                <p>引用块应该被转换为以 <code>&gt;</code> 开头的Markdown格式。</p>
            </section>

            <section class="test-section">
                <h2>✨ 文本格式测试</h2>
                <p>这里测试各种文本格式：</p>
                <ul>
                    <li><strong>粗体文本</strong> - 应该转换为 **粗体**</li>
                    <li><em>斜体文本</em> - 应该转换为 *斜体*</li>
                    <li><del>删除线文本</del> - 应该转换为 ~~删除线~~</li>
                    <li><span class="highlight">高亮文本</span> - 可能需要特殊处理</li>
                    <li><strong><em>粗体斜体组合</em></strong> - 应该转换为 ***粗体斜体***</li>
                </ul>
            </section>

            <section class="test-section">
                <h2>➖ 分隔线测试</h2>
                <p>下面应该有一条水平分隔线：</p>
                <hr>
                <p>分隔线应该被转换为 <code>---</code> 格式。</p>
            </section>
        </main>

        <footer>
            <hr>
            <p><strong>测试完成后，请检查以下内容：</strong></p>
            <ol>
                <li>所有标题是否正确转换为对应级别的Markdown语法</li>
                <li>图片是否保留了alt文本和正确的URL</li>
                <li>表格是否保持了结构和对齐</li>
                <li>链接是否转换为正确的Markdown格式</li>
                <li>代码块和行内代码是否正确标识</li>
                <li>列表是否保持了层次结构</li>
                <li>引用块是否正确格式化</li>
                <li>文本格式（粗体、斜体、删除线）是否正确</li>
                <li>分隔线是否正确转换</li>
            </ol>
            <p><em>如果发现任何问题，请检查控制台输出或联系开发团队。</em></p>
        </footer>
    </div>
</body>
</html>
