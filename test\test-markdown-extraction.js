// 测试Markdown提取功能的脚本
// 在浏览器控制台中运行此脚本来测试功能

console.log('🧪 开始测试Markdown提取功能...');

// 模拟内容提取器
class TestContentExtractor {
  constructor() {
    this.initializeExtractor();
  }

  initializeExtractor() {
    console.log('✅ 内容提取器初始化完成');
  }

  // 测试页面内容提取
  async testPageContentExtraction() {
    console.log('\n📄 测试页面内容提取...');
    
    try {
      // 查找主要内容区域
      const mainContent = this.findMainContent(document);
      console.log('✅ 找到主要内容区域:', mainContent.tagName);
      
      // 提取文本内容
      const textContent = this.extractAndCleanText(mainContent);
      console.log('✅ 提取文本内容长度:', textContent.length);
      
      // 提取Markdown内容
      const markdownContent = this.extractToMarkdown(mainContent);
      console.log('✅ 提取Markdown内容长度:', markdownContent.length);
      
      // 分析内容结构
      const contentAnalysis = this.analyzeContent(mainContent);
      console.log('✅ 内容结构分析:', contentAnalysis);
      
      return {
        textContent,
        markdownContent,
        contentAnalysis
      };
    } catch (error) {
      console.error('❌ 页面内容提取失败:', error);
      return null;
    }
  }

  // 查找主要内容区域
  findMainContent(doc) {
    const contentSelectors = [
      'main', 'article', '[role="main"]',
      '.content', '.main-content', '.post-content',
      '.article-content', '.entry-content', '.page-content',
      '#content', '#main', '#main-content'
    ];

    for (const selector of contentSelectors) {
      const element = doc.querySelector(selector);
      if (element && this.hasSignificantContent(element)) {
        return element;
      }
    }

    return doc.body || doc.documentElement;
  }

  // 检查元素是否包含有意义的内容
  hasSignificantContent(element) {
    const text = element.textContent || '';
    const wordCount = this.countWords(text);
    return wordCount > 10; // 至少10个词
  }

  // 提取和清理文本
  extractAndCleanText(element) {
    let text = element.textContent || element.innerText || '';
    
    text = text
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n')
      .replace(/^\s+|\s+$/gm, '')
      .trim();
    
    return text;
  }

  // 提取HTML结构并转换为Markdown
  extractToMarkdown(element) {
    const converter = new SimpleHTMLToMarkdownConverter();
    return converter.convert(element);
  }

  // 分析内容结构
  analyzeContent(element) {
    const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
    const paragraphs = element.querySelectorAll('p');
    const lists = element.querySelectorAll('ul, ol');
    const images = element.querySelectorAll('img');
    const tables = element.querySelectorAll('table');
    const links = element.querySelectorAll('a');
    const codeBlocks = element.querySelectorAll('pre, code');
    const blockquotes = element.querySelectorAll('blockquote');
    
    return {
      headingCount: headings.length,
      paragraphCount: paragraphs.length,
      listCount: lists.length,
      imageCount: images.length,
      tableCount: tables.length,
      linkCount: links.length,
      codeBlockCount: codeBlocks.length,
      blockquoteCount: blockquotes.length,
      hasStructure: headings.length > 0
    };
  }

  // 统计词数
  countWords(text) {
    if (!text) return 0;
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = text.replace(/[\u4e00-\u9fff]/g, '').match(/\b\w+\b/g);
    const englishWordCount = englishWords ? englishWords.length : 0;
    return chineseChars + englishWordCount;
  }
}

// 简化版HTML到Markdown转换器（用于测试）
class SimpleHTMLToMarkdownConverter {
  convert(element) {
    if (!element) return '';
    return this.convertElement(element);
  }
  
  convertElement(element) {
    if (!element) return '';
    
    if (element.nodeType === Node.TEXT_NODE) {
      return element.textContent || '';
    }
    
    const tagName = element.tagName ? element.tagName.toLowerCase() : '';
    
    switch (tagName) {
      case 'h1': return `\n\n# ${this.getTextContent(element)}\n\n`;
      case 'h2': return `\n\n## ${this.getTextContent(element)}\n\n`;
      case 'h3': return `\n\n### ${this.getTextContent(element)}\n\n`;
      case 'h4': return `\n\n#### ${this.getTextContent(element)}\n\n`;
      case 'h5': return `\n\n##### ${this.getTextContent(element)}\n\n`;
      case 'h6': return `\n\n###### ${this.getTextContent(element)}\n\n`;
      case 'p': 
        const pContent = this.convertChildren(element);
        return pContent.trim() ? `\n\n${pContent.trim()}\n\n` : '';
      case 'br': return '\n';
      case 'strong': case 'b':
        const strongContent = this.convertChildren(element);
        return strongContent.trim() ? `**${strongContent.trim()}**` : '';
      case 'em': case 'i':
        const emContent = this.convertChildren(element);
        return emContent.trim() ? `*${emContent.trim()}*` : '';
      case 'del': case 's': case 'strike':
        const delContent = this.convertChildren(element);
        return delContent.trim() ? `~~${delContent.trim()}~~` : '';
      case 'code':
        return `\`${element.textContent || ''}\``;
      case 'pre':
        const codeElement = element.querySelector('code');
        const content = codeElement ? codeElement.textContent : element.textContent;
        return `\n\n\`\`\`\n${content || ''}\n\`\`\`\n\n`;
      case 'a':
        const href = element.getAttribute('href') || '';
        const linkContent = this.convertChildren(element);
        if (href && linkContent.trim()) {
          return `[${linkContent.trim()}](${href})`;
        }
        return linkContent;
      case 'img':
        const src = element.getAttribute('src') || '';
        const alt = element.getAttribute('alt') || '';
        if (src) {
          return `![${alt}](${src})`;
        }
        return '';
      case 'ul': case 'ol':
        return this.convertList(element);
      case 'blockquote':
        const quoteContent = this.convertChildren(element);
        const lines = quoteContent.trim().split('\n');
        const quotedLines = lines.map(line => `> ${line.trim()}`);
        return `\n\n${quotedLines.join('\n')}\n\n`;
      case 'table':
        return this.convertTable(element);
      case 'hr':
        return '\n\n---\n\n';
      default:
        return this.convertChildren(element);
    }
  }
  
  convertChildren(element) {
    if (!element || !element.childNodes) return '';
    let result = '';
    for (const child of element.childNodes) {
      result += this.convertElement(child);
    }
    return result;
  }
  
  getTextContent(element) {
    return (element.textContent || '').trim();
  }
  
  convertList(element) {
    const isOrdered = element.tagName.toLowerCase() === 'ol';
    const items = Array.from(element.children).filter(child => 
      child.tagName && child.tagName.toLowerCase() === 'li'
    );
    
    if (items.length === 0) return '';
    
    let result = '\n\n';
    items.forEach((item, index) => {
      const content = this.convertChildren(item).trim();
      if (content) {
        const prefix = isOrdered ? `${index + 1}. ` : '- ';
        result += `${prefix}${content}\n`;
      }
    });
    result += '\n';
    
    return result;
  }
  
  convertTable(element) {
    const rows = Array.from(element.querySelectorAll('tr'));
    if (rows.length === 0) return '';
    
    let markdown = '\n\n';
    
    rows.forEach((row, rowIndex) => {
      const cells = Array.from(row.querySelectorAll('td, th'));
      const cellContents = cells.map(cell => {
        const content = this.convertChildren(cell);
        return content.replace(/\n/g, ' ').trim();
      });
      
      markdown += `| ${cellContents.join(' | ')} |\n`;
      
      if (rowIndex === 0) {
        const separators = cells.map(() => '---');
        markdown += `| ${separators.join(' | ')} |\n`;
      }
    });
    
    markdown += '\n';
    return markdown;
  }
}

// 运行测试
async function runTests() {
  console.log('🚀 开始运行Markdown提取测试...\n');
  
  const extractor = new TestContentExtractor();
  const result = await extractor.testPageContentExtraction();
  
  if (result) {
    console.log('\n📊 测试结果摘要:');
    console.log('- 文本内容长度:', result.textContent.length);
    console.log('- Markdown内容长度:', result.markdownContent.length);
    console.log('- 结构分析:', result.contentAnalysis);
    
    console.log('\n📝 提取的Markdown内容预览:');
    console.log(result.markdownContent.substring(0, 500) + '...');
    
    console.log('\n✅ 测试完成！');
    return result;
  } else {
    console.log('\n❌ 测试失败！');
    return null;
  }
}

// 自动运行测试
runTests().then(result => {
  if (result) {
    window.testResult = result;
    console.log('\n💾 测试结果已保存到 window.testResult');
  }
});
