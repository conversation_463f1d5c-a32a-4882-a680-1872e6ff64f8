<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API密钥测试工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        input, select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        button:hover {
            background: #005a87;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .current-config {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .config-item {
            margin-bottom: 8px;
        }
        .config-label {
            font-weight: bold;
            color: #495057;
        }
        .config-value {
            color: #6c757d;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔑 API密钥测试工具</h1>
        
        <div class="current-config">
            <h3>当前配置</h3>
            <div class="config-item">
                <span class="config-label">硬编码API密钥:</span>
                <span class="config-value" id="hardcodedKey">加载中...</span>
            </div>
            <div class="config-item">
                <span class="config-label">存储的API密钥:</span>
                <span class="config-value" id="storedKey">加载中...</span>
            </div>
            <div class="config-item">
                <span class="config-label">API基础URL:</span>
                <span class="config-value" id="baseUrl">加载中...</span>
            </div>
        </div>

        <div class="form-group">
            <label for="apiKey">测试API密钥:</label>
            <input type="password" id="apiKey" placeholder="输入要测试的API密钥">
        </div>

        <div class="form-group">
            <label for="baseUrlInput">API基础URL:</label>
            <input type="text" id="baseUrlInput" value="https://dashscope.aliyuncs.com/compatible-mode/v1">
        </div>

        <div class="form-group">
            <label for="model">模型:</label>
            <select id="model">
                <option value="qwen-plus">qwen-plus</option>
                <option value="qwen-turbo">qwen-turbo</option>
                <option value="qwen-max">qwen-max</option>
            </select>
        </div>

        <button onclick="testApiKey()">测试API密钥</button>
        <button onclick="testCurrentConfig()">测试当前配置</button>
        <button onclick="updateHardcodedKey()">更新硬编码密钥</button>
        <button onclick="loadCurrentConfig()">刷新配置</button>

        <div id="result"></div>
    </div>

    <script>
        // 加载当前配置
        async function loadCurrentConfig() {
            try {
                // 显示硬编码配置（从background script获取）
                const response = await chrome.runtime.sendMessage({ action: 'getHardcodedConfig' });
                if (response && response.success) {
                    document.getElementById('hardcodedKey').textContent = 
                        response.config.apiKey ? `${response.config.apiKey.substring(0, 10)}...` : '未设置';
                    document.getElementById('baseUrl').textContent = response.config.baseUrl;
                }

                // 显示存储的配置
                const config = await chrome.storage.sync.get(['apiConfig']);
                if (config.apiConfig) {
                    document.getElementById('storedKey').textContent = 
                        config.apiConfig.apiKey ? `${config.apiConfig.apiKey.substring(0, 10)}...` : '未设置';
                } else {
                    document.getElementById('storedKey').textContent = '未设置';
                }
            } catch (error) {
                console.error('加载配置失败:', error);
                showResult('加载配置失败: ' + error.message, 'error');
            }
        }

        // 测试API密钥
        async function testApiKey() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const baseUrl = document.getElementById('baseUrlInput').value.trim();
            const model = document.getElementById('model').value;

            if (!apiKey) {
                showResult('请输入API密钥', 'error');
                return;
            }

            showResult('正在测试API密钥...', 'loading');

            try {
                const testConfig = {
                    apiKey: apiKey,
                    baseUrl: baseUrl,
                    model: model
                };

                const response = await chrome.runtime.sendMessage({
                    action: 'testConnection',
                    apiConfig: testConfig
                });

                if (response.success) {
                    showResult(`✅ API密钥测试成功!\n\n响应: ${response.message}`, 'success');
                } else {
                    showResult(`❌ API密钥测试失败!\n\n错误: ${response.message}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 测试过程中发生错误:\n\n${error.message}`, 'error');
            }
        }

        // 测试当前配置
        async function testCurrentConfig() {
            showResult('正在测试当前配置...', 'loading');

            try {
                const config = await chrome.storage.sync.get(['apiConfig']);
                if (!config.apiConfig || !config.apiConfig.apiKey) {
                    showResult('❌ 当前没有配置API密钥', 'error');
                    return;
                }

                const response = await chrome.runtime.sendMessage({
                    action: 'testConnection',
                    apiConfig: config.apiConfig
                });

                if (response.success) {
                    showResult(`✅ 当前配置测试成功!\n\n响应: ${response.message}`, 'success');
                } else {
                    showResult(`❌ 当前配置测试失败!\n\n错误: ${response.message}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 测试过程中发生错误:\n\n${error.message}`, 'error');
            }
        }

        // 更新硬编码密钥
        async function updateHardcodedKey() {
            const apiKey = document.getElementById('apiKey').value.trim();
            
            if (!apiKey) {
                showResult('请先输入要设置的API密钥', 'error');
                return;
            }

            if (!confirm('确定要更新硬编码的API密钥吗？这将需要重新加载扩展。')) {
                return;
            }

            showResult('正在更新硬编码密钥...', 'loading');

            try {
                const response = await chrome.runtime.sendMessage({
                    action: 'updateHardcodedKey',
                    apiKey: apiKey
                });

                if (response && response.success) {
                    showResult('✅ 硬编码密钥更新成功!\n\n请重新加载扩展以使更改生效。', 'success');
                    setTimeout(() => {
                        loadCurrentConfig();
                    }, 1000);
                } else {
                    showResult(`❌ 更新失败: ${response ? response.error : '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 更新过程中发生错误:\n\n${error.message}`, 'error');
            }
        }

        // 显示结果
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
        }

        // 页面加载时自动加载配置
        document.addEventListener('DOMContentLoaded', loadCurrentConfig);
    </script>
</body>
</html>
