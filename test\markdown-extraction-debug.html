<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown提取功能诊断测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-content {
            border: 2px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .debug-panel {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .status-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .code-block {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        .image-test {
            text-align: center;
            margin: 20px 0;
        }
        .image-test img {
            max-width: 200px;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🔍 Markdown提取功能诊断测试</h1>
            <p>这个页面包含各种HTML元素，用于测试Chrome插件的Markdown提取功能是否完整</p>
        </header>

        <main>
            <section class="test-content">
                <h2>📋 测试内容区域</h2>
                <p>这是一个包含<strong>粗体文本</strong>、<em>斜体文本</em>和<del>删除线文本</del>的段落。</p>
                
                <h3>🔗 链接测试</h3>
                <p>这里有一个<a href="https://www.example.com">外部链接</a>和一个<a href="/relative-link">相对链接</a>。</p>
                
                <h3>📊 表格测试</h3>
                <table>
                    <thead>
                        <tr>
                            <th>功能</th>
                            <th>状态</th>
                            <th>说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>标题提取</td>
                            <td>✅ 正常</td>
                            <td>H1-H6标题应该正确转换</td>
                        </tr>
                        <tr>
                            <td>文本格式</td>
                            <td>⚠️ 待测试</td>
                            <td>粗体、斜体、删除线等格式</td>
                        </tr>
                        <tr>
                            <td>链接处理</td>
                            <td>❌ 问题</td>
                            <td>相对链接转换为绝对链接</td>
                        </tr>
                    </tbody>
                </table>

                <h3>📝 列表测试</h3>
                <h4>无序列表</h4>
                <ul>
                    <li>第一个列表项</li>
                    <li>第二个列表项
                        <ul>
                            <li>嵌套列表项1</li>
                            <li>嵌套列表项2</li>
                        </ul>
                    </li>
                    <li>第三个列表项</li>
                </ul>

                <h4>有序列表</h4>
                <ol>
                    <li>步骤一：准备测试数据</li>
                    <li>步骤二：执行提取功能</li>
                    <li>步骤三：验证结果</li>
                </ol>

                <h3>💻 代码测试</h3>
                <p>这里有一些<code>内联代码</code>示例。</p>
                
                <pre><code class="language-javascript">
function testMarkdownExtraction() {
    console.log('测试Markdown提取功能');
    return {
        success: true,
        message: '测试完成'
    };
}
                </code></pre>

                <h3>📸 图片测试</h3>
                <div class="image-test">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzAwN2JmZiIvPgogIDx0ZXh0IHg9IjEwMCIgeT0iNTUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNiIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPlRlc3QgSW1hZ2U8L3RleHQ+Cjwvc3ZnPg==" alt="测试图片" title="这是一个测试图片">
                    <p><em>图片说明：这是一个用于测试的SVG图片</em></p>
                </div>

                <h3>💬 引用测试</h3>
                <blockquote>
                    <p>这是一个引用块的示例。引用块应该在Markdown中正确显示为带有">"前缀的文本。</p>
                    <p>引用块可以包含多个段落。</p>
                </blockquote>

                <h3>📏 分隔线测试</h3>
                <p>下面应该有一条分隔线：</p>
                <hr>
                <p>分隔线上方的文本。</p>
            </section>

            <section>
                <h2>🧪 测试控制面板</h2>
                <div>
                    <button class="test-button" onclick="testDirectExtraction()">测试直接提取</button>
                    <button class="test-button" onclick="testAIEnhancement()">测试AI增强</button>
                    <button class="test-button" onclick="testFullPipeline()">测试完整流程</button>
                    <button class="test-button" onclick="clearResults()">清除结果</button>
                </div>

                <div id="testResults">
                    <h3>📊 测试结果</h3>
                    <div id="resultsContent">
                        <p>点击上方按钮开始测试...</p>
                    </div>
                </div>

                <div id="debugInfo">
                    <h3>🔧 调试信息</h3>
                    <div class="debug-panel" id="debugPanel">
                        等待测试开始...
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script>
        let debugLog = [];

        function addDebugLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.push(`[${timestamp}] [${type.toUpperCase()}] ${message}`);
            updateDebugPanel();
        }

        function updateDebugPanel() {
            const panel = document.getElementById('debugPanel');
            panel.innerHTML = debugLog.slice(-20).join('\n');
            panel.scrollTop = panel.scrollHeight;
        }

        function addTestResult(title, status, details) {
            const resultsContent = document.getElementById('resultsContent');
            const statusClass = status === 'success' ? 'status-success' : 
                               status === 'error' ? 'status-error' : 'status-warning';
            
            const resultHtml = `
                <div class="${statusClass}">
                    <h4>${title}</h4>
                    <p>${details}</p>
                </div>
            `;
            
            if (resultsContent.innerHTML.includes('点击上方按钮开始测试')) {
                resultsContent.innerHTML = resultHtml;
            } else {
                resultsContent.innerHTML = resultHtml + resultsContent.innerHTML;
            }
        }

        async function testDirectExtraction() {
            addDebugLog('开始测试直接提取功能');
            
            try {
                // 检查是否在Chrome扩展环境中
                if (typeof chrome === 'undefined' || !chrome.runtime) {
                    throw new Error('不在Chrome扩展环境中');
                }

                addDebugLog('发送直接提取请求');
                
                // 模拟直接提取测试
                const testContent = document.querySelector('.test-content');
                if (testContent) {
                    const textContent = testContent.textContent;
                    const wordCount = textContent.split(/\s+/).length;
                    
                    addTestResult(
                        '直接提取测试',
                        'success',
                        `成功提取 ${wordCount} 个词，内容长度: ${textContent.length} 字符`
                    );
                    
                    addDebugLog(`提取内容长度: ${textContent.length}`);
                    addDebugLog(`词数统计: ${wordCount}`);
                } else {
                    throw new Error('未找到测试内容区域');
                }
                
            } catch (error) {
                addTestResult('直接提取测试', 'error', `错误: ${error.message}`);
                addDebugLog(`直接提取失败: ${error.message}`, 'error');
            }
        }

        async function testAIEnhancement() {
            addDebugLog('开始测试AI增强功能');
            
            try {
                if (typeof chrome === 'undefined' || !chrome.runtime) {
                    throw new Error('不在Chrome扩展环境中');
                }

                addDebugLog('发送AI增强请求');
                
                // 发送消息到background script
                const response = await new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage({
                        action: 'extractMarkdown',
                        content: document.querySelector('.test-content').textContent,
                        url: window.location.href,
                        title: document.title
                    }, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });

                if (response.success) {
                    addTestResult(
                        'AI增强测试',
                        'success',
                        `成功生成Markdown，方法: ${response.method}, 长度: ${response.markdown.length} 字符`
                    );
                    
                    addDebugLog(`AI增强成功，方法: ${response.method}`);
                    addDebugLog(`生成的Markdown长度: ${response.markdown.length}`);
                    
                    // 显示部分Markdown内容
                    const preview = response.markdown.substring(0, 200) + '...';
                    addDebugLog(`Markdown预览: ${preview}`);
                } else {
                    throw new Error(response.error || '未知错误');
                }
                
            } catch (error) {
                addTestResult('AI增强测试', 'error', `错误: ${error.message}`);
                addDebugLog(`AI增强失败: ${error.message}`, 'error');
            }
        }

        async function testFullPipeline() {
            addDebugLog('开始测试完整流程');
            
            try {
                // 先测试直接提取
                await testDirectExtraction();
                
                // 等待一秒后测试AI增强
                setTimeout(async () => {
                    await testAIEnhancement();
                }, 1000);
                
                addTestResult(
                    '完整流程测试',
                    'success',
                    '已启动完整测试流程，请查看各个步骤的结果'
                );
                
            } catch (error) {
                addTestResult('完整流程测试', 'error', `错误: ${error.message}`);
                addDebugLog(`完整流程测试失败: ${error.message}`, 'error');
            }
        }

        function clearResults() {
            document.getElementById('resultsContent').innerHTML = '<p>点击上方按钮开始测试...</p>';
            debugLog = [];
            updateDebugPanel();
            addDebugLog('测试结果已清除');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addDebugLog('Markdown提取诊断页面已加载');
            
            // 检查Chrome扩展环境
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                addDebugLog('Chrome扩展环境检测成功');
            } else {
                addDebugLog('警告: 不在Chrome扩展环境中，某些测试可能无法执行', 'warning');
            }
            
            // 分析页面内容
            const testContent = document.querySelector('.test-content');
            if (testContent) {
                const elements = {
                    headings: testContent.querySelectorAll('h1, h2, h3, h4, h5, h6').length,
                    paragraphs: testContent.querySelectorAll('p').length,
                    links: testContent.querySelectorAll('a').length,
                    images: testContent.querySelectorAll('img').length,
                    tables: testContent.querySelectorAll('table').length,
                    lists: testContent.querySelectorAll('ul, ol').length,
                    codeBlocks: testContent.querySelectorAll('pre, code').length,
                    blockquotes: testContent.querySelectorAll('blockquote').length
                };
                
                addDebugLog(`页面元素统计: ${JSON.stringify(elements)}`);
            }
        });
    </script>
</body>
</html>
