<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉认证错误处理测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .error-demo {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            color: #a8071a;
        }
        .success-demo {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            color: #389e0d;
        }
        .test-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #40a9ff;
        }
        .test-button.danger {
            background: #ff4d4f;
        }
        .test-button.danger:hover {
            background: #ff7875;
        }
        .code-block {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #52c41a; }
        .status-error { background: #ff4d4f; }
        .status-warning { background: #faad14; }
        .status-info { background: #1890ff; }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🔧 钉钉认证错误处理测试</h1>
            <p>这个页面用于测试Chrome插件对钉钉认证错误的处理能力。</p>
        </header>

        <main>
            <section class="test-section">
                <h2>📋 测试说明</h2>
                <p>当前插件已经增强了对401认证错误的处理：</p>
                <ul>
                    <li>✅ 检测到401错误时，自动清理本地认证状态</li>
                    <li>✅ 显示用户友好的错误信息</li>
                    <li>✅ 提供明确的解决建议</li>
                    <li>✅ 避免无限重试循环</li>
                </ul>
            </section>

            <section class="test-section">
                <h2>🔍 错误处理流程</h2>
                <div class="code-block">
1. API调用返回401状态码
2. 识别为认证错误 (isAuthError = true)
3. 清理本地认证状态
4. 抛出用户友好的错误信息
5. 在UI中显示重新登录提示
                </div>
            </section>

            <section class="test-section">
                <h2>🎯 测试场景</h2>
                
                <h3>场景1: 正常认证状态</h3>
                <div class="success-demo">
                    <span class="status-indicator status-success"></span>
                    用户已登录钉钉文档，API调用正常
                </div>
                <button class="test-button" onclick="testNormalAuth()">测试正常认证</button>

                <h3>场景2: 认证过期 (401错误)</h3>
                <div class="error-demo">
                    <span class="status-indicator status-error"></span>
                    钉钉认证已过期，需要重新登录
                </div>
                <button class="test-button danger" onclick="testAuthExpired()">模拟认证过期</button>

                <h3>场景3: 网络错误</h3>
                <div class="error-demo">
                    <span class="status-indicator status-warning"></span>
                    网络连接问题，无法访问API
                </div>
                <button class="test-button" onclick="testNetworkError()">模拟网络错误</button>

                <h3>场景4: 其他API错误</h3>
                <div class="error-demo">
                    <span class="status-indicator status-info"></span>
                    服务器内部错误或其他问题
                </div>
                <button class="test-button" onclick="testOtherError()">模拟其他错误</button>
            </section>

            <section class="test-section">
                <h2>📊 测试结果</h2>
                <div id="testResults">
                    <p>点击上方按钮开始测试...</p>
                </div>
            </section>

            <section class="test-section">
                <h2>🛠️ 调试信息</h2>
                <button class="test-button" onclick="showDebugInfo()">获取调试信息</button>
                <div id="debugInfo" class="code-block" style="display: none;">
                    调试信息将在这里显示...
                </div>
            </section>

            <section class="test-section">
                <h2>💡 解决方案</h2>
                <h3>如果遇到401认证错误：</h3>
                <ol>
                    <li>确保已登录钉钉文档 (docs.dingtalk.com)</li>
                    <li>检查浏览器是否允许第三方Cookie</li>
                    <li>清除浏览器缓存和Cookie后重新登录</li>
                    <li>如果问题持续，请联系技术支持</li>
                </ol>

                <h3>如果遇到其他错误：</h3>
                <ul>
                    <li>检查网络连接是否正常</li>
                    <li>确认API配置是否正确</li>
                    <li>查看浏览器控制台的详细错误信息</li>
                    <li>尝试刷新页面后重试</li>
                </ul>
            </section>
        </main>
    </div>

    <script>
        // 测试函数
        function testNormalAuth() {
            addTestResult('正常认证测试', 'success', '模拟正常的API调用，认证状态有效');
        }

        function testAuthExpired() {
            addTestResult('认证过期测试', 'error', '模拟401认证错误，应该清理本地状态并提示重新登录');
            
            // 模拟401错误处理
            setTimeout(() => {
                addTestResult('错误处理结果', 'warning', '检测到401错误，已清理认证状态，请重新登录钉钉文档');
            }, 1000);
        }

        function testNetworkError() {
            addTestResult('网络错误测试', 'error', '模拟网络连接问题，应该显示网络错误提示');
        }

        function testOtherError() {
            addTestResult('其他错误测试', 'info', '模拟服务器错误，应该显示通用错误信息');
        }

        function showDebugInfo() {
            const debugDiv = document.getElementById('debugInfo');
            debugDiv.style.display = 'block';
            debugDiv.innerHTML = `
认证状态调试信息:
- 时间戳: ${new Date().toISOString()}
- 用户代理: ${navigator.userAgent}
- Cookie支持: ${navigator.cookieEnabled ? '是' : '否'}
- 本地存储支持: ${typeof(Storage) !== "undefined" ? '是' : '否'}
- 当前域名: ${window.location.hostname}
- 协议: ${window.location.protocol}

测试环境:
- 浏览器: ${getBrowserInfo()}
- 操作系统: ${getOSInfo()}
- 屏幕分辨率: ${screen.width}x${screen.height}
            `;
        }

        function addTestResult(title, type, message) {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            
            const statusClass = `status-${type}`;
            const resultHtml = `
                <div style="margin: 10px 0; padding: 10px; border-left: 4px solid var(--${type}-color); background: #f9f9f9;">
                    <strong><span class="status-indicator ${statusClass}"></span>${title}</strong>
                    <small style="float: right; color: #666;">${timestamp}</small>
                    <br>
                    <span style="color: #666;">${message}</span>
                </div>
            `;
            
            if (resultsDiv.innerHTML.includes('点击上方按钮开始测试')) {
                resultsDiv.innerHTML = resultHtml;
            } else {
                resultsDiv.innerHTML = resultHtml + resultsDiv.innerHTML;
            }
        }

        function getBrowserInfo() {
            const ua = navigator.userAgent;
            if (ua.includes('Chrome')) return 'Chrome';
            if (ua.includes('Firefox')) return 'Firefox';
            if (ua.includes('Safari')) return 'Safari';
            if (ua.includes('Edge')) return 'Edge';
            return '未知';
        }

        function getOSInfo() {
            const ua = navigator.userAgent;
            if (ua.includes('Windows')) return 'Windows';
            if (ua.includes('Mac')) return 'macOS';
            if (ua.includes('Linux')) return 'Linux';
            return '未知';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('钉钉认证错误处理测试页面已加载');
            
            // 添加CSS变量
            const style = document.createElement('style');
            style.textContent = `
                :root {
                    --success-color: #52c41a;
                    --error-color: #ff4d4f;
                    --warning-color: #faad14;
                    --info-color: #1890ff;
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>
